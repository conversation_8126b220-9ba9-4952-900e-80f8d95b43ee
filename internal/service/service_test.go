package service

import (
	"context"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

//func newMockServiceHandler(ctrl *gomock.Controller) ServiceHandler {
//	return &serviceHandler{
//		serviceRepository:                     mocks.NewMockServiceRepository(ctrl),
//		serviceStaffAvailabilityRepository:    mocks.NewMockServiceStaffAvailabilityRepository(ctrl),
//		serviceStaffOverrideRuleRepository:    mocks.NewMockServiceStaffOverrideRuleRepository(ctrl),
//		serviceBusinessOverrideRuleRepository: mocks.NewMockServiceBusinessOverrideRuleRepository(ctrl),
//		servicePetOverrideRuleRepository:      mocks.NewMockServicePetOverrideRuleRepository(ctrl),
//		//staffServiceClient:                    mocks.NewMockStaffClient(ctrl),
//	}
//}

func mockServiceName(id int64) string {
	return "service" + strconv.FormatInt(id, 10)
}

func mockServiceCategoryId(id int64) int64 {
	return id*10 + id
}

func mockServiceType(id int64) offeringpb.ServiceType {
	return offeringpb.ServiceType(id%2 + 1)
}

func mockServiceItemType(id int64) offeringpb.ServiceItemType {
	return offeringpb.ServiceItemType(id%3 + 1)
}

func mockServicePrice(id int64) float64 {
	return float64(id * 100)
}

func mockServicePriceUnit(id int64) offeringpb.ServicePriceUnit {
	return offeringpb.ServicePriceUnit(id%3 + 1)
}

func mockServiceDuration(id int64) int32 {
	return int32(id * 10)
}

type mockServiceBriefOpt func(brief *do.ServiceBrief)

func mockServiceBriefList(ids []int64, opts ...mockServiceBriefOpt) []do.ServiceBrief {
	mockServiceBriefList := make([]do.ServiceBrief, 0)
	for _, id := range ids {
		service := &do.ServiceBrief{
			ServiceId:               id,
			CategoryId:              mockServiceCategoryId(id),
			ServiceType:             mockServiceType(id),
			ServiceItemType:         mockServiceItemType(id),
			Name:                    mockServiceName(id),
			Description:             "",
			Price:                   mockServicePrice(id),
			PriceUnit:               mockServicePriceUnit(id),
			Duration:                mockServiceDuration(id),
			TaxId:                   0,
			CreateTime:              0,
			UpdateTime:              0,
			IsDeleted:               false,
			Inactive:                false,
			ColorCode:               "",
			MaxDuration:             0,
			RequireDedicatedStaff:   false,
			RequireDedicatedLodging: false,
			Images:                  nil,
			AvailableForAllStaff:    false,
		}
		for _, opt := range opts {
			opt(service)
		}
		mockServiceBriefList = append(mockServiceBriefList, *service)
	}
	return mockServiceBriefList
}

func TestServiceHandler_BatchGetOverriddenServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	//mockServiceStaffAvailabilityRepository := mocks.NewMockServiceStaffAvailabilityRepository(ctrl)
	mockServiceStaffOverrideRuleRepository := mocks.NewMockServiceStaffOverrideRuleRepository(ctrl)
	mockServiceBusinessOverrideRuleRepository := mocks.NewMockServiceBusinessOverrideRuleRepository(ctrl)
	mockServicePetOverrideRuleRepository := mocks.NewMockServicePetOverrideRuleRepository(ctrl)
	//mockStaffClient := mocks.NewMockStaffServiceClient(ctrl)

	serviceIds := []int64{1, 2, 3}
	mockServiceBriefList := mockServiceBriefList(serviceIds)

	mockBusinessOverrideRuleMap := map[int64][]do.BusinessOverrideRule{
		1: {
			{
				BusinessId: 10,
				Price:      proto.Float64(mockServicePrice(1) + 10),
			},
			{
				BusinessId: 20,
				Duration:   proto.Int32(mockServiceDuration(1) + 10),
			},
		},
	}

	mockStaffOverrideRuleMap := map[int64][]do.StaffOverrideRule{
		2: {
			{
				BusinessId: 10,
				StaffID:    101,
				Price:      proto.Float64(mockServicePrice(2) + 20),
				Duration:   nil,
			},
			{
				BusinessId: 20,
				StaffID:    102,
				Duration:   proto.Int32(mockServiceDuration(2) + 20),
			},
		},
	}

	mockPetOverrideRuleMap := map[int64][]do.PetOverrideRule{
		3: {
			{
				PetID:    1001,
				Price:    proto.Float64(mockServicePrice(3) + 30),
				Duration: proto.Int32(mockServiceDuration(3) + 30),
			},
		},
	}

	businessOverrideCondition := []do.BusinessOverrideCondition{
		{
			BusinessId: 10,
		},
		{
			BusinessId: 20,
		},
	}

	staffOverrideCondition := []do.StaffOverrideCondition{
		{
			BusinessId: 10,
			StaffId:    101,
		},
		{
			BusinessId: 10,
			StaffId:    102,
		},
		{
			BusinessId: 20,
			StaffId:    101,
		},
		{
			BusinessId: 20,
			StaffId:    102,
		},
	}

	petOverrideCondition := []do.PetOverrideCondition{
		{
			PetId: 1001,
		},
		{
			PetId: 1002,
		},
	}

	mockServiceRepository.EXPECT().GetBriefServiceListWithServiceIds(gomock.Any(), gomock.InAnyOrder([]int64{1, 2, 3})).Return(mockServiceBriefList, nil)
	mockServiceBusinessOverrideRuleRepository.EXPECT().ListServiceBusinessOverrideRules(gomock.Any(), int64(1), map[int64][]do.BusinessOverrideCondition{
		1: businessOverrideCondition,
		3: businessOverrideCondition,
	}).Return(mockBusinessOverrideRuleMap, nil)
	mockServiceStaffOverrideRuleRepository.EXPECT().ListStaffOverrideRules(gomock.Any(), int64(1), map[int64][]do.StaffOverrideCondition{
		1: staffOverrideCondition,
		2: staffOverrideCondition,
	}).Return(mockStaffOverrideRuleMap, nil)
	mockServicePetOverrideRuleRepository.EXPECT().ListServicePetOverrideRules(gomock.Any(), int64(1), map[int64][]do.PetOverrideCondition{
		3: petOverrideCondition,
	}).Return(mockPetOverrideRuleMap, nil)

	serviceHandler := &serviceHandler{
		serviceRepository: mockServiceRepository,
		//serviceStaffAvailabilityRepository:    mockServiceStaffAvailabilityRepository,
		serviceStaffOverrideRuleRepository:    mockServiceStaffOverrideRuleRepository,
		serviceBusinessOverrideRuleRepository: mockServiceBusinessOverrideRuleRepository,
		servicePetOverrideRuleRepository:      mockServicePetOverrideRuleRepository,
	}

	expected := make([]do.ServiceWithOverrideRules, 0, 3)
	for _, id := range serviceIds {
		expected = append(expected, do.ServiceWithOverrideRules{
			ServiceID:               id,
			Name:                    mockServiceName(id),
			Description:             "",
			Price:                   mockServicePrice(id),
			PriceUnit:               mockServicePriceUnit(id),
			Duration:                proto.Int32(mockServiceDuration(id)),
			TaxID:                   0,
			ServiceType:             mockServiceType(id),
			ServiceItemType:         mockServiceItemType(id),
			CategoryID:              mockServiceCategoryId(id),
			PetOverrideRules:        mockPetOverrideRuleMap[id],
			BusinessOverrideRules:   mockBusinessOverrideRuleMap[id],
			RequireDedicatedStaff:   false,
			RequireDedicatedLodging: false,
			Inactive:                false,
			MaxDuration:             0,
			Images:                  nil,
			StaffOverrideRule:       mockStaffOverrideRuleMap[id],
			AvailableForAllStaff:    false,
			AvailableStaffIdList:    nil,
		})
	}

	result, err := serviceHandler.BatchGetOverriddenServices(context.TODO(), 1, map[int64]do.ServiceOverrideConditions{
		1: {
			PetOverrideConditions:      []do.PetOverrideCondition{},
			BusinessOverrideConditions: businessOverrideCondition,
			StaffOverrideConditions:    staffOverrideCondition,
		},
		2: {
			PetOverrideConditions:      []do.PetOverrideCondition{},
			BusinessOverrideConditions: []do.BusinessOverrideCondition{},
			StaffOverrideConditions:    staffOverrideCondition,
		},
		3: {
			PetOverrideConditions:      petOverrideCondition,
			BusinessOverrideConditions: businessOverrideCondition,
			StaffOverrideConditions:    []do.StaffOverrideCondition{},
		},
	})
	assert.NoError(t, err)
	assert.Equal(t, expected, result)

	//serviceHandler := newMockServiceHandler(ctrl)
	//
	//serviceHandler.serviceRepository.EXPECT().BatchGetServices(gomock.Any(), []int64{1, 2, 3}).Return(nil, nil)
	//
	//_, err := serviceHandler.BatchGetOverriddenServices(nil, []int64{1, 2, 3})
	//if err != nil {
	//	t.Errorf("unexpected error: %v", err)
	//}
}

func Test_serviceHandler_validateServiceToUpdate_serviceNotExist(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(nil, repository.ErrRecordNotFound)

	serviceHandler := &serviceHandler{
		serviceRepository: mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11})
	assert.Equal(t, status.Error(codes.NotFound, "service not found"), err)
}

func Test_serviceHandler_validateServiceToUpdate_serviceCompanyNotMatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(&models.MoeGroomingService{
		ID:        11,
		Name:      mockServiceName(11),
		CompanyID: 2,
	}, nil)

	serviceHandler := &serviceHandler{
		serviceRepository: mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11})
	assert.Equal(t, status.Error(codes.NotFound, "service not found"), err)
}

func Test_serviceHandler_validateServiceToUpdate_serviceCategoryNotExist(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(&models.MoeGroomingService{
		ID:        11,
		Name:      mockServiceName(11),
		CompanyID: 1,
	}, nil)

	categoryRepository := mocks.NewMockCategoryRepository(ctrl)
	categoryRepository.EXPECT().GetCategory(gomock.Any(), int64(11)).Return(nil, repository.ErrRecordNotFound)

	serviceHandler := &serviceHandler{
		categoryRepository: categoryRepository,
		serviceRepository:  mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11, CategoryId: proto.Int64(11)})
	assert.Equal(t, status.Error(codes.NotFound, "service category not found"), err)
}

func Test_serviceHandler_validateServiceToUpdate_serviceCategoryCompanyNotMatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(&models.MoeGroomingService{
		ID:        11,
		Name:      mockServiceName(11),
		CompanyID: 1,
	}, nil)

	categoryRepository := mocks.NewMockCategoryRepository(ctrl)
	categoryRepository.EXPECT().GetCategory(gomock.Any(), int64(11)).Return(&models.MoeGroomingServiceCategory{
		ID:        11,
		Name:      "category",
		CompanyID: 2,
	}, nil)

	serviceHandler := &serviceHandler{
		categoryRepository: categoryRepository,
		serviceRepository:  mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11, CategoryId: proto.Int64(11)})
	assert.Equal(t, status.Error(codes.NotFound, "service category not found"), err)
}

func Test_serviceHandler_validateServiceToUpdate_serviceNameConflict(t *testing.T) {
	oldName := "old name for service 11"
	newName := "new name for service 11"
	serviceType := offeringpb.ServiceType_SERVICE
	serviceItemType := offeringpb.ServiceItemType_BOARDING

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(&models.MoeGroomingService{
		ID:              11,
		Name:            oldName,
		CompanyID:       1,
		Type:            proto.Int32(int32(serviceType)),
		ServiceItemType: proto.Int32(int32(serviceItemType)),
	}, nil)
	mockServiceRepository.EXPECT().IsServiceNameExist(gomock.Any(), int64(1), do.NewUniqueService(
		newName,
		serviceType,
		serviceItemType,
	)).Return(true, nil)

	serviceHandler := &serviceHandler{
		serviceRepository: mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11, Name: proto.String(newName)})
	assert.Equal(t, status.Error(codes.AlreadyExists, "service name already exists"), err)
}

func Test_serviceHandler_validateServiceToUpdate_serviceNameChangeCase(t *testing.T) {
	oldName := "Service Name"
	newName := "service name"
	serviceType := offeringpb.ServiceType_SERVICE
	serviceItemType := offeringpb.ServiceItemType_BOARDING

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(&models.MoeGroomingService{
		ID:              11,
		Name:            oldName,
		CompanyID:       1,
		Type:            proto.Int32(int32(serviceType)),
		ServiceItemType: proto.Int32(int32(serviceItemType)),
	}, nil)

	serviceHandler := &serviceHandler{
		serviceRepository: mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11, Name: proto.String(newName)})
	assert.NoError(t, err)
}

func Test_serviceHandler_validateServiceToUpdate_normal(t *testing.T) {
	oldName := "old name for service 11"
	newName := "new name for service 11"
	serviceType := offeringpb.ServiceType_SERVICE
	serviceItemType := offeringpb.ServiceItemType_BOARDING

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetService(gomock.Any(), int64(11)).Return(&models.MoeGroomingService{
		ID:              11,
		Name:            oldName,
		CompanyID:       1,
		Type:            proto.Int32(int32(serviceType)),
		ServiceItemType: proto.Int32(int32(serviceItemType)),
	}, nil)
	mockServiceRepository.EXPECT().IsServiceNameExist(gomock.Any(), int64(1), do.NewUniqueService(
		newName,
		serviceType,
		serviceItemType,
	)).Return(false, nil)

	serviceHandler := &serviceHandler{
		serviceRepository: mockServiceRepository,
	}

	err := serviceHandler.validateServiceToUpdate(context.TODO(), 1, &do.ServiceUpdateOpt{ServiceID: 11, Name: proto.String(newName)})
	assert.NoError(t, err)
}

func TestServiceHandler_GetServiceWithFilterPaginated(t *testing.T) {
	cid := int64(1)
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetServicesWithFilterPaginated(gomock.Any(), cid, gomock.Any(), gomock.Any(), gomock.Any()).Return(
		int64(5), []*do.ServiceDO{
			{
				ServiceID: 1,
				Name:      "service1",
			},
			{
				ServiceID: 2,
				Name:      "service2",
			},
			{
				ServiceID: 3,
				Name:      "service3",
			},
			{
				ServiceID: 4,
				Name:      "service4",
			},
			{
				ServiceID: 5,
				Name:      "service5",
			},
		}, nil).AnyTimes()

	mockStaffOverrideRuleRepository := mocks.NewMockServiceStaffOverrideRuleRepository(ctrl)
	mockStaffOverrideRuleRepository.EXPECT().GetStaffOverrideRules(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mockStaffAvailabilityRepository := mocks.NewMockServiceStaffAvailabilityRepository(ctrl)
	mockStaffAvailabilityRepository.EXPECT().GetAvailableStaffRulesByServiceIDList(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mockServiceBundleSaleRepository := mocks.NewMockServiceBundleSaleMappingRepository(ctrl)
	mockServiceBundleSaleRepository.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.ServiceBundleSaleMapping{}, nil).AnyTimes()
	mockServiceBundleSaleRepository.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceBundleSaleRepository.EXPECT().BatchInsert(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	{
		mockServicePetCodeAvailabilityRepository := mocks.NewMockServicePetCodeAvailabilityRepository(ctrl)
		mockServicePetCodeAvailabilityRepository.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*models.MoeServicePetCodeFilter{
			{
				ID:           1,
				ServiceID:    1,
				IsWhitelist:  proto.Bool(true),
				IsAllPetCode: proto.Bool(false),
				PetCodeList:  []int64{11, 22, 33},
			},
			{
				ID:           2,
				ServiceID:    2,
				IsWhitelist:  proto.Bool(false),
				IsAllPetCode: proto.Bool(false),
				PetCodeList:  []int64{11, 22, 33},
			},
		}, nil)

		serviceHandler := &serviceHandler{
			serviceRepository:                    mockServiceRepository,
			serviceStaffOverrideRuleRepository:   mockStaffOverrideRuleRepository,
			serviceStaffAvailabilityRepository:   mockStaffAvailabilityRepository,
			servicePetCodeAvailabilityRepository: mockServicePetCodeAvailabilityRepository,
			serviceBundleSaleRepository:          mockServiceBundleSaleRepository,
		}

		expect := []*do.ServiceDO{
			{
				ServiceID: 1,
				Name:      "service1",
				Availability: do.Availability{PetCodeFilter: &do.PetCodeAvailability{
					IsWhiteList:  true,
					IsAllPetCode: false,
					PetCodeIds:   []int64{11, 22, 33},
				}},
				BundleServiceIds: []int64{},
			},
			{
				ServiceID: 2,
				Name:      "service2",
				Availability: do.Availability{PetCodeFilter: &do.PetCodeAvailability{
					IsWhiteList:  false,
					IsAllPetCode: false,
					PetCodeIds:   []int64{11, 22, 33},
				}},
				BundleServiceIds: []int64{},
			},
			{
				ServiceID: 3,
				Name:      "service3",
				Availability: do.Availability{PetCodeFilter: &do.PetCodeAvailability{
					IsWhiteList:  true,
					IsAllPetCode: true,
					PetCodeIds:   nil,
				}},
				BundleServiceIds: []int64{},
			},
			{
				ServiceID: 4,
				Name:      "service4",
				Availability: do.Availability{PetCodeFilter: &do.PetCodeAvailability{
					IsWhiteList:  true,
					IsAllPetCode: true,
					PetCodeIds:   nil,
				}},
				BundleServiceIds: []int64{},
			},
			{
				ServiceID: 5,
				Name:      "service5",
				Availability: do.Availability{PetCodeFilter: &do.PetCodeAvailability{
					IsWhiteList:  true,
					IsAllPetCode: true,
					PetCodeIds:   nil,
				}},
				BundleServiceIds: []int64{},
			},
		}

		total, serviceList, err := serviceHandler.GetServiceWithFilterPaginated(context.TODO(), 1, do.ServiceQueryFilter{
			BusinessIDList:   nil,
			ServiceIdList:    nil,
			ServiceItemTypes: nil,
			ServiceTypes:     nil,
			Inactive:         nil,
			Keyword:          nil,
		}, nil, offeringpb.ServiceOrderByType_DEFAULT)
		assert.NoError(t, err)
		assert.Equal(t, int64(5), total)
		assert.Equal(t, expect, serviceList)
	}

	{
		mockServicePetCodeAvailabilityRepository := mocks.NewMockServicePetCodeAvailabilityRepository(ctrl)
		mockServicePetCodeAvailabilityRepository.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, assert.AnError)

		serviceHandler := &serviceHandler{
			serviceRepository:                    mockServiceRepository,
			serviceStaffOverrideRuleRepository:   mockStaffOverrideRuleRepository,
			serviceStaffAvailabilityRepository:   mockStaffAvailabilityRepository,
			servicePetCodeAvailabilityRepository: mockServicePetCodeAvailabilityRepository,
		}

		_, _, err := serviceHandler.GetServiceWithFilterPaginated(context.TODO(), 1, do.ServiceQueryFilter{}, nil, offeringpb.ServiceOrderByType_DEFAULT)
		assert.Error(t, err)
	}
}

func TestServiceHandler_GetApplicableServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceStaffOverrideRuleRepository := mocks.NewMockServiceStaffOverrideRuleRepository(ctrl)
	mockServiceStaffAvailabilityRepository := mocks.NewMockServiceStaffAvailabilityRepository(ctrl)
	mockStaffClient := mocks.NewMockStaffClient(ctrl)
	mockServiceBundleSaleRepository := mocks.NewMockServiceBundleSaleMappingRepository(ctrl)
	mockServiceBundleSaleRepository.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.ServiceBundleSaleMapping{}, nil).AnyTimes()
	mockServiceBundleSaleRepository.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockServiceBundleSaleRepository.EXPECT().BatchInsert(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	serviceHandler := &serviceHandler{
		serviceRepository:                  mockServiceRepository,
		serviceStaffOverrideRuleRepository: mockServiceStaffOverrideRuleRepository,
		serviceStaffAvailabilityRepository: mockServiceStaffAvailabilityRepository,
		staffServiceClient:                 mockStaffClient,
		serviceBundleSaleRepository:        mockServiceBundleSaleRepository,
	}

	ctx := context.Background()
	companyId := int64(1)
	petId := int64(100)
	businessId := int64(10)
	filter := &do.ApplicableServiceQueryFilter{BusinessId: &businessId}
	pageNum := int32(1)
	pageSize := int32(10)
	paginationRequest := &utilsV2.PaginationRequest{PageNum: &pageNum, PageSize: &pageSize}

	tests := []struct {
		name           string
		serviceType    offeringpb.ServiceType
		setupMocks     func()
		expectedTotal  int64
		expectedResult []do.ServiceWithOverrideRules
		expectedError  string
	}{
		{
			name:          "Invalid service type",
			serviceType:   offeringpb.ServiceType_SERVICE_TYPE_UNSPECIFIED,
			setupMocks:    func() {},
			expectedError: "invalid service type",
		},
		{
			name:        "Get applicable addon services",
			serviceType: offeringpb.ServiceType_ADDON,
			setupMocks: func() {
				mockServiceRepository.EXPECT().GetApplicableAddon(ctx, companyId, petId, filter, paginationRequest).
					Return(int64(2), []do.ServiceWithOverrideRules{{ServiceID: 1}, {ServiceID: 2}}, nil)
			},
			expectedTotal:  2,
			expectedResult: []do.ServiceWithOverrideRules{{ServiceID: 1}, {ServiceID: 2}},
		},
		{
			name:        "Get applicable services successfully",
			serviceType: offeringpb.ServiceType_SERVICE,
			setupMocks: func() {
				mockServices := []do.ServiceWithOverrideRules{
					{ServiceID: 1, AvailableForAllStaff: true},
					{ServiceID: 2, AvailableForAllStaff: false},
					{ServiceID: 3, AvailableForAllStaff: false},
				}
				mockServiceRepository.EXPECT().GetApplicableService(ctx, companyId, petId, filter, paginationRequest).
					Return(int64(3), mockServices, nil)

				mockServiceStaffOverrideRuleRepository.EXPECT().GetStaffOverrideRules(ctx, companyId, &businessId, []int64{1, 2, 3}).
					Return(map[int64][]do.StaffOverrideRule{
						1: {{StaffID: 101, Price: proto.Float64(100)}},
						2: {{StaffID: 102, Duration: proto.Int32(60)}},
					}, nil)

				mockStaffClient.EXPECT().ListAllStaffIds(ctx, companyId, &businessId).
					Return([]int64{101, 102, 103}, nil)

				mockServiceStaffAvailabilityRepository.EXPECT().GetAvailableStaffRulesByServiceIDList(ctx, []int64{1, 2, 3}).
					Return([]do.ServiceStaffAvailabilityDO{
						{ServiceId: 2, StaffId: 102},
						{ServiceId: 2, StaffId: 103},
					}, nil)
			},
			expectedTotal: 3,
			expectedResult: []do.ServiceWithOverrideRules{
				{
					ServiceID:            1,
					AvailableForAllStaff: true,
					AvailableStaffIdList: []int64{101, 102, 103},
					StaffOverrideRule:    []do.StaffOverrideRule{{StaffID: 101, Price: proto.Float64(100)}},
					BundleServiceIds:     []int64{},
				},
				{
					ServiceID:            2,
					AvailableForAllStaff: false,
					AvailableStaffIdList: []int64{102, 103},
					StaffOverrideRule:    []do.StaffOverrideRule{{StaffID: 102, Duration: proto.Int32(60)}},
					BundleServiceIds:     []int64{},
				},
				{
					ServiceID:            3,
					AvailableForAllStaff: false,
					AvailableStaffIdList: []int64{},
					StaffOverrideRule:    nil,
					BundleServiceIds:     []int64{},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			total, result, err := serviceHandler.GetApplicableServices(ctx, companyId, petId, tt.serviceType, filter, paginationRequest)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTotal, total)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

func TestServiceHandler_GetMaxServicePriceByLodgingType(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockCompanyServiceClient := mocks.NewMockCompanyServiceClient(ctrl)

	tests := []struct {
		name             string
		companyId        int64
		businessId       int64
		lodgingTypeId    int64
		mockServices     []*do.ServiceDO
		mockCurrency     string
		serviceRepoErr   error
		companyErr       error
		expectedPrice    float64
		expectedCurrency string
		expectError      bool
	}{
		{
			name:          "Normal case with multiple services",
			companyId:     1,
			businessId:    10,
			lodgingTypeId: 100,
			mockServices: []*do.ServiceDO{
				{
					ServiceID: 1,
					Price:     150.0,
					Availability: do.Availability{
						LodgingFilter:      proto.Bool(true),
						CustomizedLodgings: []int64{100, 200},
					},
				},
				{
					ServiceID: 2,
					Price:     250.0,
					Availability: do.Availability{
						LodgingFilter:      proto.Bool(true),
						CustomizedLodgings: []int64{100, 300},
					},
				},
				{
					ServiceID: 3,
					Price:     300.0,
					Availability: do.Availability{
						LodgingFilter:      proto.Bool(true),
						CustomizedLodgings: []int64{200, 300}, // Not applicable for lodgingTypeId 100
					},
				},
			},
			mockCurrency:     "USD",
			expectedPrice:    250.0,
			expectedCurrency: "USD",
			expectError:      false,
		},
		{
			name:          "Service without lodging filter (applicable to all)",
			companyId:     1,
			businessId:    10,
			lodgingTypeId: 100,
			mockServices: []*do.ServiceDO{
				{
					ServiceID: 1,
					Price:     100.0,
					Availability: do.Availability{
						LodgingFilter: nil, // No filter means applicable to all
					},
				},
				{
					ServiceID: 2,
					Price:     300.0,
					Availability: do.Availability{
						LodgingFilter: proto.Bool(false), // No filter means applicable to all
					},
				},
			},
			mockCurrency:     "USD",
			expectedPrice:    300.0,
			expectedCurrency: "USD",
			expectError:      false,
		},
		{
			name:          "No applicable services",
			companyId:     1,
			businessId:    10,
			lodgingTypeId: 100,
			mockServices: []*do.ServiceDO{
				{
					ServiceID: 1,
					Price:     150.0,
					Availability: do.Availability{
						LodgingFilter:      proto.Bool(true),
						CustomizedLodgings: []int64{200, 300}, // Not applicable for lodgingTypeId 100
					},
				},
			},
			mockCurrency:     "USD",
			expectedPrice:    0.0,
			expectedCurrency: "USD",
			expectError:      false,
		},
		{
			name:             "Empty service list",
			companyId:        1,
			businessId:       10,
			lodgingTypeId:    100,
			mockServices:     []*do.ServiceDO{},
			mockCurrency:     "USD",
			expectedPrice:    0.0,
			expectedCurrency: "USD",
			expectError:      false,
		},
		{
			name:           "Service repository error",
			companyId:      1,
			businessId:     10,
			lodgingTypeId:  100,
			serviceRepoErr: assert.AnError,
			expectError:    true,
		},
		{
			name:          "Company service client error",
			companyId:     1,
			businessId:    10,
			lodgingTypeId: 100,
			mockServices: []*do.ServiceDO{
				{
					ServiceID: 1,
					Price:     150.0,
					Availability: do.Availability{
						LodgingFilter: nil,
					},
				},
			},
			companyErr:  assert.AnError,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceHandler := &serviceHandler{
				serviceRepository:    mockServiceRepository,
				companyServiceClient: clients.NewCompanyClient(mockCompanyServiceClient),
			}

			// Mock service repository call
			if tt.serviceRepoErr != nil {
				mockServiceRepository.EXPECT().
					GetServicesWithFilterPaginated(
						gomock.Any(),
						tt.companyId,
						do.ServiceQueryFilter{
							BusinessIDList:   []int64{tt.businessId},
							ServiceItemTypes: []offeringpb.ServiceItemType{offeringpb.ServiceItemType_BOARDING, offeringpb.ServiceItemType_DAYCARE},
							ServiceTypes:     []offeringpb.ServiceType{offeringpb.ServiceType_SERVICE},
							Inactive:         proto.Bool(false),
						},
						nil,
						offeringpb.ServiceOrderByType_DEFAULT,
					).
					Return(int64(0), nil, tt.serviceRepoErr)
			} else {
				mockServiceRepository.EXPECT().
					GetServicesWithFilterPaginated(
						gomock.Any(),
						tt.companyId,
						do.ServiceQueryFilter{
							BusinessIDList:   []int64{tt.businessId},
							ServiceItemTypes: []offeringpb.ServiceItemType{offeringpb.ServiceItemType_BOARDING, offeringpb.ServiceItemType_DAYCARE},
							ServiceTypes:     []offeringpb.ServiceType{offeringpb.ServiceType_SERVICE},
							Inactive:         proto.Bool(false),
						},
						nil,
						offeringpb.ServiceOrderByType_DEFAULT,
					).
					Return(int64(len(tt.mockServices)), tt.mockServices, nil)

				// Mock company service client call only if service repo succeeds
				if tt.companyErr != nil {
					mockCompanyServiceClient.EXPECT().
						GetCompanyPreferenceSetting(
							gomock.Any(),
							&organizationsvcpb.GetCompanyPreferenceSettingRequest{
								CompanyId: tt.companyId,
							},
						).
						Return(nil, tt.companyErr)
				} else {
					mockCompanyServiceClient.EXPECT().
						GetCompanyPreferenceSetting(
							gomock.Any(),
							&organizationsvcpb.GetCompanyPreferenceSettingRequest{
								CompanyId: tt.companyId,
							},
						).
						Return(&organizationsvcpb.GetCompanyPreferenceSettingResponse{
							PreferenceSetting: &organizationpb.CompanyPreferenceSettingModel{
								CurrencyCode: tt.mockCurrency,
							},
						}, nil)
				}
			}

			result, err := serviceHandler.GetMaxServicePriceByLodgingType(context.Background(), tt.companyId, tt.businessId, tt.lodgingTypeId)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedCurrency, result.CurrencyCode)
				assert.Equal(t, int64(tt.expectedPrice), result.Units)
				assert.Equal(t, int32(0), result.Nanos)
			}
		})
	}
}

func TestServiceHandler_UpdateService_Bundle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	sqlDB, sqlMock, err := sqlmock.New()
	require.NoError(t, err)
	defer sqlDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       sqlDB,
		DriverName: "postgres",
	})
	db, err := gorm.Open(dialector, &gorm.Config{})
	require.NoError(t, err)

	mockServiceRepo := mocks.NewMockServiceRepository(ctrl)
	mockStaffAvailRepo := mocks.NewMockServiceStaffAvailabilityRepository(ctrl)
	mockStaffOverrideRepo := mocks.NewMockServiceStaffOverrideRuleRepository(ctrl)
	mockPetCodeAvailRepo := mocks.NewMockServicePetCodeAvailabilityRepository(ctrl)
	mockBundleRepo := mocks.NewMockServiceBundleSaleMappingRepository(ctrl)

	h := &serviceHandler{
		serviceRepository:                    mockServiceRepo,
		serviceStaffAvailabilityRepository:   mockStaffAvailRepo,
		serviceStaffOverrideRuleRepository:   mockStaffOverrideRepo,
		servicePetCodeAvailabilityRepository: mockPetCodeAvailRepo,
		serviceBundleSaleRepository:          mockBundleRepo,
		offeringDB:                           db,
	}

	ctx := context.Background()
	companyID := int64(1)
	serviceID := int64(100)
	avail := &do.Availability{}

	// mock validateServiceToUpdate 依赖
	mockServiceRepo.EXPECT().GetService(ctx, serviceID).Return(&models.MoeGroomingService{
		ID:        int32(serviceID),
		CompanyID: companyID,
		Name:      "test",
	}, nil).AnyTimes()

	mockServiceRepo.EXPECT().UpdateService(ctx, companyID, gomock.Any()).Return(nil).AnyTimes()
	mockStaffAvailRepo.EXPECT().UpdateServiceStaffAvailability(ctx, serviceID, gomock.Any()).Return(int64(0), nil).AnyTimes()
	mockStaffOverrideRepo.EXPECT().UpdateServiceStaffOverrideRules(ctx, companyID, serviceID, gomock.Any()).Return(int64(0), nil).AnyTimes()

	t.Run("bundle ids not empty", func(t *testing.T) {
		opt := &do.ServiceUpdateOpt{
			ServiceID:        serviceID,
			Availability:     avail,
			BundleServiceIds: []int64{2, 3},
		}
		sqlMock.ExpectBegin()
		mockBundleRepo.EXPECT().Delete(ctx, gomock.Any(), companyID, serviceID).Return(nil).AnyTimes()
		mockBundleRepo.EXPECT().BatchInsert(ctx, gomock.Any(), companyID, serviceID, []int64{2, 3}).Return(nil).AnyTimes()
		sqlMock.ExpectCommit()
		err := h.UpdateService(ctx, companyID, opt)
		require.NoError(t, err)
	})

	t.Run("bundle ids empty", func(t *testing.T) {
		opt := &do.ServiceUpdateOpt{
			ServiceID:        serviceID,
			Availability:     avail,
			BundleServiceIds: []int64{},
		}
		sqlMock.ExpectBegin()
		mockBundleRepo.EXPECT().Delete(ctx, gomock.Any(), companyID, serviceID).Return(nil).AnyTimes()
		sqlMock.ExpectCommit()
		err := h.UpdateService(ctx, companyID, opt)
		require.NoError(t, err)
	})

	t.Run("bundle transaction error", func(t *testing.T) {
		opt := &do.ServiceUpdateOpt{
			ServiceID:        serviceID,
			Availability:     avail,
			BundleServiceIds: []int64{2, 3},
		}
		sqlMock.ExpectBegin()
		mockBundleRepo.EXPECT().Delete(ctx, gomock.Any(), companyID, serviceID).Return(assert.AnError).AnyTimes()
		sqlMock.ExpectRollback()
		err := h.UpdateService(ctx, companyID, opt)
		require.Error(t, err)
	})
}
