package converter

import (
	"encoding/json"
	"fmt"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

var ServiceConverter = &impl.ServiceConverter{}

func ConvertCreateServiceDefToDO(source *offeringpb.CreateServiceDef) *do.ServiceDO {
	serviceDO := ServiceConverter.ConvertCreateDefToDO(source)

	if len(source.LocationStaffOverrideList) > 0 {
		serviceDO.Availability.LocationOverrideList, serviceDO.Availability.StaffOverrideList = ConvertLocationStaffOverrideListToDO(source.LocationStaffOverrideList)
	}

	return serviceDO
}

func ConvertUpdateServiceDefToDO(source *offeringpb.UpdateServiceDef) *do.ServiceUpdateOpt {
	serviceDO := ServiceConverter.ConvertUpdateDefToUpdateOpt(source)

	if len(source.LocationStaffOverrideList) > 0 {
		serviceDO.Availability.LocationOverrideList, serviceDO.Availability.StaffOverrideList = ConvertLocationStaffOverrideListToDO(source.LocationStaffOverrideList)
	}

	return serviceDO
}

// ConvertLocationStaffOverrideListToDO 新增的LocationStaffOverrideList字段携带LocationOverrideList和StaffOverrideList的信息，需单独处理并覆盖原有的LocationOverrideList
func ConvertLocationStaffOverrideListToDO(source []*offeringpb.LocationStaffOverrideRule) ([]do.BusinessOverrideRule, []do.StaffOverrideRule) {
	locationOverrideRules := make([]do.BusinessOverrideRule, 0)
	staffOverrideRules := make([]do.StaffOverrideRule, 0)

	for _, locationStaffOverrideRule := range source {
		locationOverrideRules = append(locationOverrideRules, ConvertLocationStaffOverrideRuleToDO(locationStaffOverrideRule))
		staffOverrideRules = append(staffOverrideRules, ConvertServiceStaffOverrideRuleToStaffDO(locationStaffOverrideRule)...)
	}
	return locationOverrideRules, staffOverrideRules
}

func ConvertServiceDOToPBModel(source *do.ServiceDO) *offeringpb.ServiceModel {
	pbModel := ServiceConverter.ConvertServiceDOToPBModel(source)
	pbModel.LocationStaffOverrideList = ConvertLocationStaffOverrideDOListToPB(source.Availability.LocationOverrideList, source.Availability.StaffOverrideList)
	return pbModel
}

func ConvertLocationStaffOverrideDOListToPB(locationOverrideRules []do.BusinessOverrideRule, staffOverrideRules []do.StaffOverrideRule) []*offeringpb.LocationStaffOverrideRule {

	locationStaffOverrideRules := make([]*offeringpb.LocationStaffOverrideRule, 0, len(locationOverrideRules))
	staffOverrideRulesMap := make(map[int64][]*offeringpb.StaffOverrideRule)
	for _, rule := range staffOverrideRules {
		staffOverrideRule := &offeringpb.StaffOverrideRule{
			StaffId:  rule.StaffID,
			Price:    rule.Price,
			Duration: rule.Duration,
		}
		staffOverrideRulesMap[rule.BusinessId] = append(staffOverrideRulesMap[rule.BusinessId], staffOverrideRule)
	}

	for _, rule := range locationOverrideRules {
		locationStaffOverrideRule := &offeringpb.LocationStaffOverrideRule{
			LocationOverride: &offeringpb.LocationOverrideRule{
				BusinessId:  rule.BusinessId,
				Price:       rule.Price,
				Duration:    rule.Duration,
				MaxDuration: rule.MaxDuration,
				TaxId:       rule.TaxID,
			},
			StaffOverrideList: staffOverrideRulesMap[rule.BusinessId],
		}
		locationStaffOverrideRules = append(locationStaffOverrideRules, locationStaffOverrideRule)
		delete(staffOverrideRulesMap, rule.BusinessId)
	}

	// 将剩余的staffOverrideRules转换为LocationStaffOverrideRule
	for businessId, staffOverrideRuleList := range staffOverrideRulesMap {
		locationStaffOverrideRule := &offeringpb.LocationStaffOverrideRule{
			LocationOverride: &offeringpb.LocationOverrideRule{
				BusinessId: businessId,
			},
			StaffOverrideList: staffOverrideRuleList,
		}
		locationStaffOverrideRules = append(locationStaffOverrideRules, locationStaffOverrideRule)
	}

	return locationStaffOverrideRules
}

func convertBreedBindingModelListToDO(breedBindingModels []models.MoeGroomingServiceBreedBinding) []do.PetTypeAndBreedAvailabilityRule {
	rules := make([]do.PetTypeAndBreedAvailabilityRule, 0, len(breedBindingModels))
	for _, breedBindingModel := range breedBindingModels {
		rule := do.PetTypeAndBreedAvailabilityRule{
			PetTypeID: proto.Int64(int64(*breedBindingModel.PetTypeID)),
			IsAll:     breedBindingModel.IsAll,
			Breeds:    breedBindingModel.GetBreedingNameList(),
		}
		rules = append(rules, rule)
	}
	return rules
}

func convertAddonAvailabilityRuleListToDO(addOnAvailabilityRuleList []models.MoeGroomingAddonApplicableService) []do.AddonAvailabilityRule {
	rules := make([]do.AddonAvailabilityRule, 0, len(addOnAvailabilityRuleList))
	for _, addOnAvailabilityRule := range addOnAvailabilityRuleList {
		rule := do.AddonAvailabilityRule{
			ServiceItemType:         offeringpb.ServiceItemType(addOnAvailabilityRule.ServiceItemType).Enum(),
			AvailableForAllServices: addOnAvailabilityRule.AvailableForAllServices,
			AvailableServiceIdList:  addOnAvailabilityRule.GetAllowedServiceList(),
		}
		rules = append(rules, rule)
	}
	return rules

}

func ConvertDBModelToServiceDO(
	serviceModel models.MoeGroomingService,
	breedBindingModelList []models.MoeGroomingServiceBreedBinding,
	coatBindingModel *models.MoeGroomingServiceCoatBinding,
	locationOverrideModelList []models.MoeGroomingServiceLocation,
	addOnAvailabilityRuleList []models.MoeGroomingAddonApplicableService,
	autoRolloverRule *models.MoeAutoRollover,
) *do.ServiceDO {
	service := &do.ServiceDO{
		ServiceID:   int64(serviceModel.ID),
		Name:        serviceModel.Name,
		Description: serviceModel.GetDescription(),
		CategoryId:  int64(serviceModel.CategoryID),
		ColorCode:   serviceModel.ColorCode,
		Inactive:    *serviceModel.Inactive,
		Images:      serviceModel.GetImages(),
		Availability: do.Availability{
			AvailableForAllStaff:    serviceModel.IsAllStaff,
			IsAllLocation:           serviceModel.IsAllLocation,
			LocationOverrideList:    ServiceConverter.ConvertLocationOverrideRuleModelListToDO(locationOverrideModelList),
			RequireDedicatedLodging: serviceModel.RequireDedicatedLodging,
			LodgingFilter:           serviceModel.LodgingFilter,
			CustomizedLodgings:      serviceModel.GetCustomizedLodgingList(),
			RequireDedicatedStaff:   serviceModel.RequireDedicatedStaff,
			BreedFilter:             serviceModel.BreedFilter,
			CustomizedBreed:         convertBreedBindingModelListToDO(breedBindingModelList),
			WeightFilter:            serviceModel.WeightFilter,
			PetSizeFilter:           serviceModel.PetSizeFilter,
			CustomizedPetSizes:      serviceModel.GetCustomizedPetSizeList(),
			AvailableMinWeight:      serviceModel.WeightDownLimit,
			AvailableMaxWeight:      serviceModel.WeightUpLimit,
			CoatFilter:              serviceModel.CoatFilter,
			CustomizedCoat:          coatBindingModel.GetCoatIDList(),
		},
		Price:       serviceModel.Price,
		PriceUnit:   offeringpb.ServicePriceUnit(*serviceModel.PriceUnit),
		ServiceType: offeringpb.ServiceType(*serviceModel.Type),
		TaxId:       int64(serviceModel.TaxID),
		Duration:    &serviceModel.Duration,
		MaxDuration: proto.Int32(serviceModel.MaxDuration),
		Commission: do.Commission{
			AddToCommissionBase: serviceModel.AddToCommission,
			CanTip:              serviceModel.CanTip,
		},
		ServiceItemType: offeringpb.ServiceItemType(*serviceModel.ServiceItemType),
		AddonAvailability: &do.AddonAvailability{
			ServiceFilter:     serviceModel.ServiceFilter,
			ServiceFilterList: convertAddonAvailabilityRuleListToDO(addOnAvailabilityRuleList),
		},
		AutoRolloverRule:           ConvertAutoRolloverRuleDBModelToDO(autoRolloverRule),
		CreateTime:                 serviceModel.CreateTime,
		UpdateTime:                 serviceModel.UpdateTime,
		IsDeleted:                  serviceModel.IsDeleted(),
		CompanyId:                  serviceModel.CompanyID,
		Source:                     offeringpb.ServiceModel_Source(*serviceModel.Source),
		NumSessions:                serviceModel.NumSessions,
		DurationSessionMin:         serviceModel.DurationSessionMin,
		Capacity:                   serviceModel.Capacity,
		IsRequirePrerequisiteClass: *serviceModel.IsRequirePrerequisiteClass,
		PrerequisiteClassIds:       serviceModel.PrerequisiteClassIds,
		IsEvaluationRequired:       lo.FromPtrOr(serviceModel.IsEvaluationRequired, false),
		IsEvaluationRequiredForOb:  lo.FromPtrOr(serviceModel.IsEvaluationRequiredForOb, false),
		EvaluationId:               serviceModel.EvaluationID,
		AdditionalServiceRule:      serviceModel.AdditionalServiceRule,
	}
	return service
}

func ConvertInt64ListMapToSlice(source map[int64]*utilsV2.Int64List) map[int64][]int64 {
	result := make(map[int64][]int64, len(source))
	for key, value := range source {
		result[key] = value.Values
	}
	return result
}

func convertStaffOverrideRuleDOToPB(source do.StaffOverrideRule) *offeringpb.StaffOverrideRule {
	return &offeringpb.StaffOverrideRule{
		StaffId:  source.StaffID,
		Price:    source.Price,
		Duration: source.Duration,
	}
}

func ConvertOverriddenServiceToPBWithCondition(condition *offeringsvcpb.CustomizedServiceQueryCondition, service do.ServiceWithOverrideRules) *offeringpb.CustomizedServiceView {
	// 1. set service price & duration with company level setting
	// 2. apply location override rule
	// 3. apply customer override rule
	result := &offeringpb.CustomizedServiceView{
		Id:                      service.ServiceID,
		Name:                    service.Name,
		Description:             service.Description,
		Price:                   service.Price,
		PriceUnit:               service.PriceUnit,
		Duration:                service.Duration,
		Type:                    service.ServiceType,
		CategoryId:              service.CategoryID,
		PriceOverrideType:       offeringpb.ServiceOverrideType_SERVICE_OVERRIDE_TYPE_UNSPECIFIED,
		DurationOverrideType:    offeringpb.ServiceOverrideType_SERVICE_OVERRIDE_TYPE_UNSPECIFIED,
		TaxId:                   service.TaxID,
		ServiceItemType:         service.ServiceItemType,
		RequireDedicatedLodging: service.RequireDedicatedLodging,
		RequireDedicatedStaff:   service.RequireDedicatedStaff,
		Inactive:                service.Inactive,
		MaxDuration:             service.MaxDuration,
		Images:                  service.Images,
		AvailableStaffs: &offeringpb.CustomizedServiceView_AvailableStaffs{
			IsAllAvailable: service.AvailableForAllStaff,
			Ids:            service.AvailableStaffIdList,
		},
		StaffOverrideList: lo.Map(service.StaffOverrideRule, func(item do.StaffOverrideRule, _ int) *offeringpb.StaffOverrideRule {
			return convertStaffOverrideRuleDOToPB(item)
		}),
		LodgingFilter:         service.LodgingFilter,
		CustomizedLodgings:    service.CustomizedLodgings,
		BundleServiceIds:      service.BundleServiceIds,
		AdditionalServiceRule: service.AdditionalServiceRule,
	}

	if len(service.BusinessOverrideRules) > 0 {
		for _, r := range service.BusinessOverrideRules {
			if condition != nil && condition.GetBusinessId() != r.BusinessId {
				continue
			}
			if r.Price != nil {
				result.Price = *r.Price
				result.PriceOverrideType = offeringpb.ServiceOverrideType_LOCATION
			}
			if r.Duration != nil {
				result.Duration = r.Duration
				result.DurationOverrideType = offeringpb.ServiceOverrideType_LOCATION
			}
			if r.TaxID != nil {
				result.TaxId = *r.TaxID
			}
			if r.MaxDuration != nil {
				result.MaxDuration = *r.MaxDuration
			}
		}
	}

	// staff override 的优先级更高，可以覆盖 location override 的结果
	if len(service.StaffOverrideRule) > 0 {
		for _, r := range service.StaffOverrideRule {
			if condition != nil && (condition.GetBusinessId() != r.BusinessId || condition.GetStaffId() != r.StaffID) {
				continue
			}
			if r.Price != nil {
				result.Price = *r.Price
				result.PriceOverrideType = offeringpb.ServiceOverrideType_STAFF
			}
			if r.Duration != nil {
				result.Duration = r.Duration
				result.DurationOverrideType = offeringpb.ServiceOverrideType_STAFF
			}
		}
	}

	// address override
	if len(service.AddressOverrideRules) > 0 {
		for _, r := range service.AddressOverrideRules {
			if condition != nil && condition.GetZipcode() != r.Zipcode {
				continue
			}
			if r.Effect != nil {
				result.Price = calculatePriceForFloat(result.Price, r.Effect)
				result.PriceOverrideType = offeringpb.ServiceOverrideType_ZONE
			}
		}
	}

	// pet override 的优先级更高，可以覆盖 location override 的结果
	if len(service.PetOverrideRules) > 0 {
		for _, r := range service.PetOverrideRules {
			if condition != nil && condition.GetPetId() != r.PetID {
				continue
			}
			if r.Price != nil {
				result.Price = *r.Price
				result.PriceOverrideType = offeringpb.ServiceOverrideType_CLIENT
			}
			if r.Duration != nil {
				result.Duration = r.Duration
				result.DurationOverrideType = offeringpb.ServiceOverrideType_CLIENT
			}
		}
	}

	return result

}

func ConvertApplicableQueryToDO(source *offeringsvcpb.GetApplicableServiceListRequest) *do.ApplicableServiceQueryFilter {
	filter := &do.ApplicableServiceQueryFilter{
		BusinessId:      source.BusinessId,
		ServiceItemType: source.ServiceItemType,
		Inactive:        source.Inactive,
		Zipcode:         source.Zipcode, // for zone pricing rules
	}

	if source.Keyword != nil {
		filter.Keyword = proto.String(fmt.Sprintf("%%%s%%", *source.Keyword))
	}

	if source.GetOnlyAvailable() && source.Filter != nil {
		if petFilter := source.Filter.FilterByPet; petFilter != nil {
			filter.PetFilter = &do.PetFilter{
				PetWeight:     petFilter.PetWeight,
				PetSizeId:     petFilter.PetSizeId,
				PetTypeId:     petFilter.GetPetType().Enum(),
				PetBreed:      petFilter.PetBreed,
				PetCoatTypeId: petFilter.PetCoatTypeId,
				PetCodeIds:    petFilter.PetCodeIds,
			}
		}

		if serviceFilter := source.Filter.FilterByService; serviceFilter != nil {
			filter.SelectedServiceFilter = &do.SelectedServiceFilter{
				SelectedServiceIdList:   serviceFilter.GetServiceIds(),
				SelectedServiceItemType: serviceFilter.ServiceItemType,
			}
		}

		if lodgingFilter := source.Filter.FilterByLodging; lodgingFilter != nil {
			filter.LodgingFilter = &do.LodgingFilter{
				SelectedLodgingTypeIdList: lodgingFilter.GetLodgingTypeIds(),
			}
		}
	}

	return filter
}

func ConvertPBPetOverrideToDO(source *offeringsvcpb.OverrideServiceRequest) map[int64][]do.PetOverrideRule {
	result := make(map[int64][]do.PetOverrideRule, len(source.OverrideRulesByServiceId))
	for serviceId, rule := range source.OverrideRulesByServiceId {
		petOverrideRules := make([]do.PetOverrideRule, 0, len(rule.GetPetOverrideList()))
		for _, rule := range rule.GetPetOverrideList() {
			petOverrideRules = append(petOverrideRules, do.PetOverrideRule{
				PetID:    rule.PetId,
				Price:    rule.Price,
				Duration: rule.Duration,
			})
		}
		result[serviceId] = petOverrideRules
	}
	return result
}

func ConvertServiceModelToBriefDO(service models.MoeGroomingService) do.ServiceBrief {
	return do.ServiceBrief{
		ServiceId:                  int64(service.ID),
		CategoryId:                 int64(service.CategoryID),
		ServiceType:                offeringpb.ServiceType(*service.Type),
		ServiceItemType:            offeringpb.ServiceItemType(*service.ServiceItemType),
		Name:                       service.Name,
		Description:                service.GetDescription(),
		Price:                      service.Price,
		PriceUnit:                  offeringpb.ServicePriceUnit(*service.PriceUnit),
		Duration:                   service.Duration,
		TaxId:                      int64(service.TaxID),
		CreateTime:                 service.CreateTime,
		UpdateTime:                 service.UpdateTime,
		IsDeleted:                  service.IsDeleted(),
		Inactive:                   *service.Inactive,
		ColorCode:                  *service.ColorCode,
		MaxDuration:                service.MaxDuration,
		RequireDedicatedStaff:      *service.RequireDedicatedStaff,
		RequireDedicatedLodging:    *service.RequireDedicatedLodging,
		LodgingFilter:              *service.LodgingFilter,
		CustomizedLodgings:         service.GetCustomizedLodgingList(),
		Images:                     service.GetImages(),
		AvailableForAllStaff:       *service.IsAllStaff,
		NumSessions:                service.NumSessions,
		DurationSessionMin:         service.DurationSessionMin,
		Capacity:                   service.Capacity,
		IsRequirePrerequisiteClass: *service.IsRequirePrerequisiteClass,
		PrerequisiteClassIds:       service.PrerequisiteClassIds,
		IsEvaluationRequired:       *service.IsEvaluationRequired,
		IsEvaluationRequiredForOb:  *service.IsEvaluationRequiredForOb,
		EvaluationId:               service.EvaluationID,
		CompanyId:                  service.CompanyID,
	}
}

func ConvertToServiceWithOverrideRules(basicService do.ServiceBrief, locationOverrideRules []do.BusinessOverrideRule, petOverrideRules []do.PetOverrideRule, staffOverrideRules []do.StaffOverrideRule, addressOverrideRules []do.AddressOverrideRule) do.ServiceWithOverrideRules {
	return do.ServiceWithOverrideRules{
		ServiceID:               basicService.ServiceId,
		Name:                    basicService.Name,
		Description:             basicService.Description,
		Price:                   basicService.Price,
		PriceUnit:               basicService.PriceUnit,
		Duration:                proto.Int32(basicService.Duration),
		TaxID:                   basicService.TaxId,
		ServiceType:             basicService.ServiceType,
		ServiceItemType:         basicService.ServiceItemType,
		CategoryID:              basicService.CategoryId,
		PetOverrideRules:        petOverrideRules,
		BusinessOverrideRules:   locationOverrideRules,
		RequireDedicatedStaff:   basicService.RequireDedicatedStaff,
		RequireDedicatedLodging: basicService.RequireDedicatedLodging,
		Inactive:                basicService.Inactive,
		MaxDuration:             basicService.MaxDuration,
		Images:                  basicService.Images,
		StaffOverrideRule:       staffOverrideRules,
		LodgingFilter:           basicService.LodgingFilter,
		CustomizedLodgings:      basicService.CustomizedLodgings,
		AddressOverrideRules:    addressOverrideRules,
	}
}

func ConvertServicePetOverrideRuleModelToDO(source models.MoeGroomingCustomerService) do.PetOverrideRule {
	return do.PetOverrideRule{
		PetID:    int64(source.PetID),
		Price:    source.GetSavedPrice(),
		Duration: source.GetSavedTime(),
	}
}

func ConvertServiceBusinessOverrideRuleModelToDO(source models.MoeGroomingServiceLocation) do.BusinessOverrideRule {
	return do.BusinessOverrideRule{
		BusinessId:  int64(source.BusinessID),
		Price:       source.Price,
		TaxID:       source.GetTaxID(),
		Duration:    source.Duration,
		MaxDuration: source.MaxDuration,
	}
}

func ConvertServiceStaffOverrideRuleModelToDO(source models.ServiceStaffOverrideRule) do.StaffOverrideRule {
	return do.StaffOverrideRule{
		BusinessId: source.BusinessID,
		StaffID:    source.StaffID,
		Price:      source.Price,
		Duration:   source.Duration,
	}
}

func ConvertServiceStaffOverrideRuleToStaffDO(source *offeringpb.LocationStaffOverrideRule) []do.StaffOverrideRule {
	businessId := source.LocationOverride.BusinessId

	staffOverrideRules := make([]do.StaffOverrideRule, 0, len(source.StaffOverrideList))
	for _, staffOverrideRule := range source.StaffOverrideList {
		staffOverrideRules = append(staffOverrideRules, do.StaffOverrideRule{
			BusinessId: businessId,
			StaffID:    staffOverrideRule.StaffId,
			Price:      staffOverrideRule.Price,
			Duration:   staffOverrideRule.Duration,
		})
	}

	return staffOverrideRules
}

func ConvertLocationStaffOverrideRuleToDO(source *offeringpb.LocationStaffOverrideRule) do.BusinessOverrideRule {
	return do.BusinessOverrideRule{
		BusinessId:  source.LocationOverride.BusinessId,
		Duration:    source.LocationOverride.Duration,
		Price:       source.LocationOverride.Price,
		MaxDuration: source.LocationOverride.MaxDuration,
		TaxID:       source.LocationOverride.TaxId,
	}
}

func ConvertServiceStaffOverrideRuleDoToModel(companyId, serviceId int64, source []do.StaffOverrideRule) []models.ServiceStaffOverrideRule {
	var staffOverrideRules []models.ServiceStaffOverrideRule
	for _, staffOverrideRule := range source {
		serviceStaffOverrideRule := models.ServiceStaffOverrideRule{
			CompanyID:  companyId,
			ServiceID:  serviceId,
			BusinessID: staffOverrideRule.BusinessId,
			Price:      staffOverrideRule.Price,
			StaffID:    staffOverrideRule.StaffID,
			Duration:   staffOverrideRule.Duration,
		}
		staffOverrideRules = append(staffOverrideRules, serviceStaffOverrideRule)
	}
	return staffOverrideRules
}

func ConvertServiceOverrideConditionToDO(conditions []*offeringsvcpb.CustomizedServiceQueryCondition) map[int64]do.ServiceOverrideConditions {
	result := make(map[int64]do.ServiceOverrideConditions, 0)

	for _, cond := range conditions {
		serviceCondition, ok := result[cond.ServiceId]
		if !ok {
			serviceCondition = do.ServiceOverrideConditions{}
		}

		if cond.PetId != nil {
			serviceCondition.PetOverrideConditions = append(serviceCondition.PetOverrideConditions, do.PetOverrideCondition{
				PetId: cond.GetPetId(),
			})
		}

		if cond.BusinessId != nil {
			serviceCondition.BusinessOverrideConditions = append(serviceCondition.BusinessOverrideConditions, do.BusinessOverrideCondition{
				BusinessId: cond.GetBusinessId(),
			})
		}

		if cond.StaffId != nil {
			serviceCondition.StaffOverrideConditions = append(serviceCondition.StaffOverrideConditions, do.StaffOverrideCondition{
				BusinessId: cond.GetBusinessId(),
				StaffId:    cond.GetStaffId(),
			})
		}

		if cond.GetZipcode() != "" {
			serviceCondition.AddressOverrideCondition = append(serviceCondition.AddressOverrideCondition, do.AddressOverrideCondition{
				Zipcode: cond.GetZipcode(),
			})
		}

		result[cond.ServiceId] = serviceCondition
	}

	return result
}

func ConvertServiceWithOverrideInfoToPB(queryConditions []*offeringsvcpb.CustomizedServiceQueryCondition, servicesWithOverrideInfo []do.ServiceWithOverrideRules) []*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo {
	serviceMap := lo.SliceToMap(servicesWithOverrideInfo, func(service do.ServiceWithOverrideRules) (int64, do.ServiceWithOverrideRules) {
		return service.ServiceID, service
	})

	result := make([]*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, 0, len(queryConditions))
	for _, cond := range queryConditions {
		service, ok := serviceMap[cond.ServiceId]
		if !ok {
			continue
		}

		result = append(result, &offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo{
			QueryCondition:    cond,
			CustomizedService: ConvertOverriddenServiceToPBWithCondition(cond, service),
		})
	}

	return result
}

func CovertMoeGroomingServiceCoatBinding(companyID int64, serviceID int64, customizedCoat []int64) *models.MoeGroomingServiceCoatBinding {
	coatIdListStr := `[]`
	if len(customizedCoat) != 0 {
		coatIdListJSON, _ := json.Marshal(customizedCoat)
		coatIdListStr = string(coatIdListJSON)
	}
	return &models.MoeGroomingServiceCoatBinding{
		ServiceID:  int32(serviceID),
		CoatIDList: proto.String(coatIdListStr),
		CompanyID:  companyID,
	}
}

func ConvertMoeAutoRollover(serviceId int64, autoRolloverRule do.AutoRolloverRule) *models.MoeAutoRollover {
	return &models.MoeAutoRollover{
		ServiceID:       serviceId,
		Enabled:         proto.Bool(autoRolloverRule.Enabled),
		AfterMinute:     autoRolloverRule.AfterMinute,
		TargetServiceID: autoRolloverRule.TargetServiceId,
	}
}

func ConvertMoeGroomingServiceLocation(companyID int64, serviceID int64, overrideRule *do.BusinessOverrideRule) *models.MoeGroomingServiceLocation {
	record := &models.MoeGroomingServiceLocation{
		CompanyID:  companyID,
		BusinessID: int32(overrideRule.BusinessId),
		ServiceID:  int32(serviceID),
		IsDeleted:  proto.Bool(false),
	}
	if overrideRule.Price != nil {
		record.Price = overrideRule.Price
	}
	if overrideRule.Duration != nil {
		record.Duration = overrideRule.Duration
	}
	if overrideRule.TaxID != nil {
		record.TaxID = proto.Int32(int32(*overrideRule.TaxID))
	}
	if overrideRule.MaxDuration != nil {
		record.MaxDuration = overrideRule.MaxDuration
	}
	return record
}

// calculatePriceForFloat applies pricing effect to a float64 price value
func calculatePriceForFloat(originalPrice float64, effect *offeringModelsV2.Effect) float64 {
	originalPriceDecimal := decimal.NewFromFloat(originalPrice)
	adjustedPrice := calculatePrice(originalPriceDecimal, effect)
	return adjustedPrice.InexactFloat64()
}

// calculatePrice applies pricing effect to a decimal price value
func calculatePrice(originalPrice decimal.Decimal, effect *offeringModelsV2.Effect) decimal.Decimal {
	switch effect.GetType() {
	case offeringModelsV2.EffectType_FIXED_DISCOUNT:
		adjustment := decimal.NewFromFloat(effect.GetValue())
		return decimal.Max(originalPrice.Sub(adjustment), decimal.Zero)
	case offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT:
		percentage := decimal.NewFromFloat(1 - effect.GetValue()/100)
		if effect.RoundingPosition != nil {
			return originalPrice.Mul(percentage).RoundBank(*effect.RoundingPosition)
		}
		return originalPrice.Mul(percentage).RoundBank(2)
	case offeringModelsV2.EffectType_FIXED_INCREASE:
		adjustment := decimal.NewFromFloat(effect.GetValue())
		return originalPrice.Add(adjustment)
	case offeringModelsV2.EffectType_PERCENTAGE_INCREASE:
		percentage := decimal.NewFromFloat(1 + effect.GetValue()/100)
		if effect.RoundingPosition != nil {
			return originalPrice.Mul(percentage).RoundBank(*effect.RoundingPosition)
		}
		return originalPrice.Mul(percentage).RoundBank(2)
	}
	return originalPrice
}
