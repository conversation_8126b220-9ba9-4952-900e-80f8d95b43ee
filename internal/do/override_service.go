package do

import (
	offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
)

type ServiceOverrideConditions struct {
	PetOverrideConditions      []PetOverrideCondition
	BusinessOverrideConditions []BusinessOverrideCondition
	StaffOverrideConditions    []StaffOverrideCondition
	AddressOverrideCondition   []AddressOverrideCondition
}

type AddressOverrideCondition struct {
	Zipcode         string
	ServiceType     offeringV1.ServiceType
	ServiceItemType offeringV1.ServiceItemType
}

type PetOverrideCondition struct {
	PetId int64
}

type BusinessOverrideCondition struct {
	BusinessId int64
}

type StaffOverrideCondition struct {
	BusinessId int64
	StaffId    int64
}

type ServiceWithPetCustomized struct {
	ServiceBrief
	CustomizedPrice    *float64
	CustomizedDuration *int32
}

type PetCustomizedRecord struct {
	ServiceId  int64
	BusinessId int64
	Price      *float64
	Duration   *int32
	SaveType   int32
	CreateTime int64
	UpdateTime int64
}

type ServiceWithOverrideRules struct {
	ServiceID               int64
	Name                    string
	Description             string
	Price                   float64
	PriceUnit               offeringV1.ServicePriceUnit
	Duration                *int32
	TaxID                   int64
	ServiceType             offeringV1.ServiceType
	ServiceItemType         offeringV1.ServiceItemType
	CategoryID              int64
	PetOverrideRules        []PetOverrideRule
	BusinessOverrideRules   []BusinessOverrideRule
	RequireDedicatedStaff   bool
	RequireDedicatedLodging bool
	Inactive                bool
	MaxDuration             int32
	Images                  []string
	StaffOverrideRule       []StaffOverrideRule
	AvailableForAllStaff    bool
	AvailableStaffIdList    []int64
	LodgingFilter           bool
	CustomizedLodgings      []int64
	BundleServiceIds        []int64
	AdditionalServiceRule   *offeringV1.AdditionalServiceRule
	AddressOverrideRules    []AddressOverrideRule
}

type PetOverrideRule struct {
	PetID    int64
	Price    *float64
	Duration *int32
}

type BusinessOverrideRule struct {
	BusinessId  int64
	Price       *float64
	TaxID       *int64
	Duration    *int32
	MaxDuration *int32
}

type StaffOverrideRule struct {
	BusinessId int64
	StaffID    int64
	Price      *float64
	Duration   *int32
}

type AddressOverrideRule struct {
	Zipcode string
	Effect  *offeringModelsV2.Effect
}
